# Splitter - Сервис A/B тестирования

Сервис для проведения A/B тестов с использованием Unleash feature flags. Работает как прокси-сервер, который направляет пользователей в разные группы эксперимента и перенаправляет их на соответствующие URL или проксирует запросы.

## Логика работы

### Основной алгоритм

1. **Идентификация пользователя**: При первом запросе создается уникальный `device_id` и сохраняется в cookie
2. **Определение группы**: Используется Unleash client для определения, в какую группу попадает пользователь:
   - **Группа A (Monolit)**: Запросы проксируются на основной сервис
   - **Группа B (MF)**: Пользователь перенаправляется на альтернативный URL
3. **Отправка аналитики**: Для HTML-страниц отправляются события в систему аналитики
4. **Обработка запросов**: В зависимости от группы выполняется проксирование или редирект

### Компоненты

- **HTTP сервер** с graceful shutdown
- **Reverse proxy** для группы A
- **Система аналитики** с асинхронной отправкой событий
- **Health check** endpoint
- **Unleash integration** для feature flagging

## Переменные окружения

### Основные настройки

| Переменная | Значение по умолчанию | Описание |
|------------|----------------------|----------|
| `PORT` | `8080` | Порт для HTTP сервера |
| `APP_VERSION` | `staging` | Версия приложения |

### Настройки эксперимента

| Переменная | Значение по умолчанию | Описание |
|------------|----------------------|----------|
| `EXPERIMENT_NAME` | `monolit-mf-ab-test` | Название эксперимента для аналитики |
| `EXPERIMENT_GROUP_A` | `Monolit` | Название группы A (контрольная) |
| `EXPERIMENT_GROUP_B` | `MF` | Название группы B (экспериментальная) |
| `EXPERIMENT_EVENT_HIT` | `experiments.hit` | Название события для аналитики |

### URL конфигурация

| Переменная | Значение по умолчанию | Описание |
|------------|----------------------|----------|
| `PROXY_URL` | `https://ya.ru` | URL для проксирования группы A |
| `REDIRECT_BASE_URL` | `https://google.com` | Базовый URL для редиректа группы B |
| `SOGU_URL` | `https://sogu-staging.sogu.dev.tripster.tech/events/` | URL для отправки аналитики |

### Unleash настройки

| Переменная | Значение по умолчанию | Описание |
|------------|----------------------|----------|
| `UNLEASH_URL` | *(пусто)* | URL Unleash сервера |
| `UNLEASH_API_KEY` | *(пусто)* | API ключ для Unleash |
| `UNLEASH_APP_NAME` | `splitter` | Имя приложения в Unleash |
| `UNLEASH_FEATURE` | `monolit-mf-ab-test` | Название feature flag |

## Endpoints

### `GET /`
Основной endpoint для A/B тестирования:
- Определяет группу пользователя через Unleash
- Отправляет аналитику для HTML-страниц
- Проксирует запросы (группа A) или делает редирект (группа B)

### `GET /health`
Health check endpoint:
- Возвращает статус сервиса
- Включает версию и текущее время
- Используется для мониторинга

## Аналитика

Система отправляет события в SOGU для отслеживания эксперимента:

### Условия отправки
- Только GET запросы
- Запросы HTML-страниц (определяется по Accept header)
- Исключаются AJAX запросы

### Структура события
```json
{
  "app_version": "staging",
  "event_name": "experiments.hit",
  "platform": "web",
  "url": "/path",
  "ga_client_id": "GA.1.123456789.123456789",
  "ya_client_id": "123456789",
  "user_agent": "Mozilla/5.0...",
  "device_id": "uuid",
  "dt": **********,
  "params": {
    "device_id": "uuid",
    "experiment": "monolit-mf-ab-test",
    "variant": "Monolit",
    "was_generated": false
  }
}
```

## Запуск

### Локально
```bash
# Установка зависимостей
go mod download

# Запуск с переменными из .env
go run main.go
```

### Docker
```bash
# Сборка образа
docker build -t splitter .

# Запуск контейнера
docker run -p 8080:8080 --env-file .env splitter
```

## Мониторинг

- **Логирование**: Структурированные JSON логи
- **Health check**: `GET /health`
- **Graceful shutdown**: Обработка SIGINT/SIGTERM
- **Timeouts**: Настроенные таймауты для HTTP клиента и сервера

## Безопасность

- **HttpOnly cookies**: Device ID сохраняется в защищенной cookie
- **Secure cookies**: Используется Secure flag для HTTPS
- **SameSite**: Защита от CSRF атак
- **Таймауты**: Защита от медленных запросов

## Архитектура

### Поток запросов

```
Пользователь → Splitter → Unleash (определение группы)
                ↓
         Группа A: Proxy → PROXY_URL
         Группа B: Redirect → REDIRECT_BASE_URL
                ↓
         Аналитика → SOGU_URL (асинхронно)
```

### Cookie управление

- `device_id`: Уникальный идентификатор пользователя (30 дней)
- `_ga`: Google Analytics Client ID (для аналитики)
- `_ym_uid`: Yandex Metrica Client ID (для аналитики)

## Особенности реализации

### Определение HTML-страниц

Сервис анализирует запросы для определения, нужно ли отправлять аналитику:

1. **Accept header**: Проверяется наличие `text/html`
2. **AJAX исключение**: Исключаются запросы с `X-Requested-With: XMLHttpRequest`
3. **Fallback**: Если нет Accept header, анализируется путь (отсутствие расширения файла)

### Graceful Shutdown

- Обработка сигналов SIGINT/SIGTERM
- Закрытие Unleash client
- Завершение HTTP сервера с таймаутом 30 секунд

### Error Handling

- Логирование всех ошибок в структурированном JSON формате
- Fallback значения для всех конфигурационных параметров
- Обработка ошибок прокси с возвратом 502 Bad Gateway